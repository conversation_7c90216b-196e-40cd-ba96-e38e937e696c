                            {% extends 'base.html' %}

{% block title %}Admin Dashboard - {{ block.super }}{% endblock %}



{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Quick Stats -->
    <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-lg font-semibold text-gray-700 mb-2">Total Students</h3>
        <p class="text-3xl font-bold text-primary-600">1,234</p>
    </div>
    <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-lg font-semibold text-gray-700 mb-2">Total Courses</h3>
        <p class="text-3xl font-bold text-primary-600">567</p>
    </div>
    <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-lg font-semibold text-gray-700 mb-2">Advising Sessions</h3>
        <p class="text-3xl font-bold text-primary-600">89</p>
    </div>
    <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-lg font-semibold text-gray-700 mb-2">Pending Reports</h3>
        <p class="text-3xl font-bold text-primary-600">12</p>
    </div>
</div>

<div class="mt-8">
    <h2 class="text-2xl font-semibold text-gray-800 mb-4">Recent Activity</h2>
    <div class="bg-white p-6 rounded-lg shadow-md">
        <!-- Activity feed will be loaded dynamically here -->
        <div hx-get="{% url 'api_recent_activity' %}" hx-trigger="load" hx-swap="innerHTML">
            <div class="htmx-indicator">Loading recent activity...</div>
        </div>
    </div>
</div>
{% endblock %}

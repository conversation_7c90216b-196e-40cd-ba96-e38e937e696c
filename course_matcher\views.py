from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.models import User
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db import models
from django.views.decorators.http import require_http_methods
from .forms import StudentRegistrationForm, AcademicRecordForm, StudentInterestsForm, CareerGoalsForm
from .models import StudentProfile, Course, AcademicRecord, Recommendation, Department
from .recommendation_service import RecommendationEngine
import json

def student_registration(request):
    if request.method == 'POST':
        form = StudentRegistrationForm(request.POST)
        if form.is_valid():
            # Create User with all the required details
            user = User.objects.create_user(
                username=form.cleaned_data['username'],
                email=form.cleaned_data['email'],
                password=form.cleaned_data['password'],
                first_name=form.cleaned_data.get('first_name', ''),
                last_name=form.cleaned_data.get('last_name', '')
            )

            # Create StudentProfile from the form, linking it to the new user
            student_profile = form.save(commit=False)
            student_profile.user = user
            student_profile.save()

            # It's good practice to call save_m2m() when using commit=False, in case of M2M fields.
            form.save_m2m()

            return redirect('login')  # Redirect to login page after successful registration
    else:
        form = StudentRegistrationForm()
    return render(request, 'registration/register.html', {'form': form})

def redirect_after_login(request):
    if request.user.is_staff or request.user.is_superuser:
        return redirect('management_dashboard')
    else:
        return redirect('student_dashboard')

def landing_page(request):
    if request.user.is_authenticated:
        return redirect('redirect_after_login')
    return render(request, 'landing_page.html')

@login_required
def home(request):
    """Student dashboard with overview metrics and quick actions"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        # Redirect to complete profile if student profile doesn't exist
        messages.warning(request, "Please complete your student profile.")
        return redirect('student_registration')

    # Get dashboard data
    recent_records = AcademicRecord.objects.filter(student=student).order_by('-date_enrolled')[:5]
    recent_recommendations = Recommendation.objects.filter(student=student, is_dismissed=False).order_by('-confidence_score')[:3]

    # Calculate progress metrics
    total_courses = AcademicRecord.objects.filter(student=student).count()
    completed_courses = AcademicRecord.objects.filter(student=student, grade__isnull=False).count()

    context = {
        'student': student,
        'recent_records': recent_records,
        'recent_recommendations': recent_recommendations,
        'total_courses': total_courses,
        'completed_courses': completed_courses,
        'interests_count': len(student.interests) if student.interests else 0,
        'has_career_goals': bool(student.career_goals),
    }

    return render(request, 'student/dashboard.html', context)

@login_required
def student_academic_records(request):
    """Academic records management with CRUD operations"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Student profile not found.")
        return redirect('student_dashboard')

    if request.method == 'POST':
        form = AcademicRecordForm(request.POST, student=student)
        if form.is_valid():
            record = form.save(commit=False)
            record.student = student
            record.save()
            messages.success(request, f"Academic record for {record.course.code} added successfully!")
            return redirect('academic_records')
    else:
        form = AcademicRecordForm(student=student)

    # Get existing records
    records = AcademicRecord.objects.filter(student=student).order_by('-year', '-semester', 'course__code')

    # Group records by semester for better display
    records_by_semester = {}
    for record in records:
        semester_key = f"{record.semester.title()} {record.year}"
        if semester_key not in records_by_semester:
            records_by_semester[semester_key] = []
        records_by_semester[semester_key].append(record)

    context = {
        'student': student,
        'form': form,
        'records': records,
        'records_by_semester': records_by_semester,
        'total_credits': student.total_credits,
        'gpa': student.gpa,
    }

    return render(request, 'student/academic_records.html', context)

@login_required
def student_interests(request):
    """Student interests management interface"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Student profile not found.")
        return redirect('student_dashboard')

    if request.method == 'POST':
        form = StudentInterestsForm(request.POST, student=student)
        if form.is_valid():
            form.save()
            messages.success(request, "Your interests have been updated successfully!")
            return redirect('student_interests')
    else:
        form = StudentInterestsForm(student=student)

    # Get suggested interests based on student's major and existing courses
    suggested_interests = []
    if student.major:
        # Get topics from courses in the student's major
        major_courses = Course.objects.filter(department=student.major)
        all_topics = []
        for course in major_courses:
            if course.topics:
                all_topics.extend(course.topics)

        # Get most common topics that aren't already in student's interests
        from collections import Counter
        topic_counts = Counter(all_topics)
        current_interests = student.interests or []
        suggested_interests = [topic for topic, count in topic_counts.most_common(10)
                             if topic not in current_interests][:5]

    context = {
        'student': student,
        'form': form,
        'current_interests': student.interests or [],
        'suggested_interests': suggested_interests,
    }

    return render(request, 'student/interests.html', context)

@login_required
def career_goals(request):
    """Career goals and preferences management"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Student profile not found.")
        return redirect('student_dashboard')

    if request.method == 'POST':
        form = CareerGoalsForm(request.POST, instance=student)
        if form.is_valid():
            form.save()
            messages.success(request, "Your career goals have been updated successfully!")
            return redirect('career_goals')
    else:
        form = CareerGoalsForm(instance=student)

    # Get career-related course suggestions based on goals
    career_suggestions = []
    if student.career_goals:
        # Simple keyword matching for career-related courses
        career_keywords = student.career_goals.lower().split()
        for course in Course.objects.all():
            course_text = f"{course.name} {course.description}".lower()
            if any(keyword in course_text for keyword in career_keywords if len(keyword) > 3):
                career_suggestions.append(course)
        career_suggestions = career_suggestions[:5]  # Limit to 5 suggestions

    context = {
        'student': student,
        'form': form,
        'career_suggestions': career_suggestions,
    }

    return render(request, 'student/career_goals.html', context)

@login_required
def recommendations(request):
    """Course recommendations with personalized suggestions"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Student profile not found.")
        return redirect('student_dashboard')

    # Generate recommendations using the recommendation engine
    engine = RecommendationEngine()

    # Get or create fresh recommendations
    if request.GET.get('refresh') == 'true':
        # Clear existing recommendations and generate new ones
        Recommendation.objects.filter(student=student).delete()

    existing_recommendations = Recommendation.objects.filter(
        student=student,
        is_dismissed=False
    ).order_by('-confidence_score')

    if not existing_recommendations.exists():
        # Generate new recommendations
        try:
            new_recommendations = engine.get_recommendations(student, limit=10)
            # Save recommendations to database
            for rec in new_recommendations:
                Recommendation.objects.create(
                    student=student,
                    course=rec['course'],
                    confidence_score=rec['confidence_score'],
                    recommendation_type=rec['recommendation_type'],
                    reasoning=rec['reasoning']
                )
            existing_recommendations = Recommendation.objects.filter(
                student=student,
                is_dismissed=False
            ).order_by('-confidence_score')
        except Exception as e:
            messages.warning(request, "Unable to generate recommendations at this time. Please try again later.")
            existing_recommendations = []

    # Group recommendations by confidence level
    high_confidence = existing_recommendations.filter(confidence_score__gte=0.7)
    medium_confidence = existing_recommendations.filter(confidence_score__gte=0.4, confidence_score__lt=0.7)
    low_confidence = existing_recommendations.filter(confidence_score__lt=0.4)

    context = {
        'student': student,
        'high_confidence': high_confidence,
        'medium_confidence': medium_confidence,
        'low_confidence': low_confidence,
        'total_recommendations': existing_recommendations.count(),
    }

    return render(request, 'student/recommendations.html', context)

# HTMX action views for dynamic interactions
@login_required
@require_http_methods(["POST"])
def bookmark_course(request, course_id):
    """Bookmark/unbookmark a course (placeholder for future functionality)"""
    try:
        course = get_object_or_404(Course, id=course_id)
        # This could be implemented with a Bookmark model in the future
        return JsonResponse({'status': 'success', 'message': f'Course {course.code} bookmarked!'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

@login_required
@require_http_methods(["DELETE"])
def delete_interest(request, interest_id):
    """Remove an interest from student's profile"""
    try:
        student = request.user.studentprofile
        interests = student.interests or []

        # Remove interest by index (interest_id is the index)
        if 0 <= interest_id < len(interests):
            removed_interest = interests.pop(interest_id)
            student.interests = interests
            student.save()
            return JsonResponse({'status': 'success', 'message': f'Removed "{removed_interest}"'})
        else:
            return JsonResponse({'status': 'error', 'message': 'Interest not found'}, status=404)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

@login_required
@require_http_methods(["POST"])
def add_interest(request):
    """Add a new interest to student's profile"""
    try:
        student = request.user.studentprofile
        new_interest = request.POST.get('interest', '').strip()

        if not new_interest:
            return JsonResponse({'status': 'error', 'message': 'Interest cannot be empty'}, status=400)

        interests = student.interests or []
        if new_interest not in interests:
            interests.append(new_interest)
            student.interests = interests
            student.save()
            return JsonResponse({'status': 'success', 'message': f'Added "{new_interest}"'})
        else:
            return JsonResponse({'status': 'error', 'message': 'Interest already exists'}, status=400)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

@login_required
@require_http_methods(["POST"])
def add_suggested_interest(request):
    """Add a suggested interest to student's profile"""
    try:
        student = request.user.studentprofile
        suggested_interest = request.POST.get('interest', '').strip()

        if not suggested_interest:
            return JsonResponse({'status': 'error', 'message': 'Interest cannot be empty'}, status=400)

        interests = student.interests or []
        if suggested_interest not in interests:
            interests.append(suggested_interest)
            student.interests = interests
            student.save()
            return JsonResponse({'status': 'success', 'message': f'Added "{suggested_interest}"'})
        else:
            return JsonResponse({'status': 'error', 'message': 'Interest already exists'}, status=400)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

@login_required
@require_http_methods(["DELETE"])
def delete_academic_record(request, record_id):
    """Delete an academic record"""
    try:
        student = request.user.studentprofile
        record = get_object_or_404(AcademicRecord, id=record_id, student=student)
        course_code = record.course.code
        record.delete()
        return JsonResponse({'status': 'success', 'message': f'Removed {course_code} from your records'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

@login_required
@require_http_methods(["POST"])
def add_academic_record(request):
    """Add a new academic record via HTMX"""
    try:
        student = request.user.studentprofile
        form = AcademicRecordForm(request.POST, student=student)

        if form.is_valid():
            record = form.save(commit=False)
            record.student = student
            record.save()
            return JsonResponse({
                'status': 'success',
                'message': f'Added {record.course.code} to your academic records'
            })
        else:
            errors = []
            for field, error_list in form.errors.items():
                for error in error_list:
                    errors.append(f"{field}: {error}")
            return JsonResponse({'status': 'error', 'message': '; '.join(errors)}, status=400)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

# Legacy placeholder views (keeping for compatibility)
def update_career_goal(request, goal_id):
    return HttpResponse(status=204)

def add_career_goal(request):
    return HttpResponse(status=204)

def delete_career_goal(request, goal_id):
    return HttpResponse(status=204)

# Admin Views
def admin_dashboard(request):
    return render(request, 'admin/dashboard.html')

def admin_students(request):
    return render(request, 'admin/students.html')

def admin_courses(request):
    return render(request, 'admin/courses.html')

def admin_advising(request):
    return render(request, 'admin/advising.html')

def admin_reports(request):
    return render(request, 'admin/reports.html')

# HTMX API Views
def recent_activity_api(request):
    # This is a placeholder; a real implementation would query the database
    # for recent advising sessions, student registrations, etc.
    return HttpResponse("""<ul>
            <li class='border-b py-2'>Student <strong>John Doe</strong> registered.</li>
            <li class='border-b py-2'>Course <strong>'Intro to Python'</strong> was updated.</li>
            <li class='py-2'>New advising session created for <strong>Jane Smith</strong>.</li>
        </ul>""")

def student_list_api(request):
    students = StudentProfile.objects.select_related('user', 'major').all()[:50] # Limiting for performance
    return render(request, 'admin/partials/_student_list.html', {'students': students})

def search_students_api(request):
    search_text = request.POST.get('search', '').strip()
    
    if search_text:
        # A more comprehensive search would query across multiple fields
        students = StudentProfile.objects.select_related('user', 'major').filter(
            models.Q(user__first_name__icontains=search_text) |
            models.Q(user__last_name__icontains=search_text) |
            models.Q(student_id__icontains=search_text) |
            models.Q(major__name__icontains=search_text)
        )[:50]
    else:
        students = StudentProfile.objects.select_related('user', 'major').all()[:50]
        
    return render(request, 'admin/partials/_student_list.html', {'students': students})

def course_list_api(request):
    courses = Course.objects.select_related('department').all()[:50]
    return render(request, 'admin/partials/_course_list.html', {'courses': courses})

def search_courses_api(request):
    search_text = request.POST.get('search', '').strip()
    
    if search_text:
        courses = Course.objects.select_related('department').filter(
            models.Q(name__icontains=search_text) |
            models.Q(code__icontains=search_text) |
            models.Q(department__name__icontains=search_text)
        )[:50]
    else:
        courses = Course.objects.select_related('department').all()[:50]
        
    return render(request, 'admin/partials/_course_list.html', {'courses': courses})

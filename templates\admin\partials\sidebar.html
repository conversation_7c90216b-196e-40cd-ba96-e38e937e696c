
    <div class="h-full flex flex-col">
        <!-- Mobile close button -->
        <div class="sidebar-close-btn lg:hidden">
            <button @click="sidebarOpen = false" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <!-- Header/App Title -->
        <div class="px-6 border-b border-gray-200 flex items-center" style="height:72px;min-height:72px;">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div>
                    <h2 class="text-lg font-bold text-gray-900">Admin Portal</h2>
                    <p class="text-xs text-gray-500">Course Management</p>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="flex-1 px-4 py-6 space-y-2">
            <a href="{% url 'management_dashboard' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-primary-50 hover:text-primary-700"
               up-target="main" up-transition="cross-fade">
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-primary-600"
                     fill="currentColor" viewBox="0 0 20 20">
                    <rect x="3" y="3" width="14" height="14" rx="3" fill="currentColor" />
                </svg>
                <span>Dashboard</span>
            </a>
            <a href="{% url 'management_students' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-primary-50 hover:text-primary-700"
               up-target="main" up-transition="cross-fade">
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-primary-600"
                     fill="currentColor" viewBox="0 0 20 20">
                    <circle cx="10" cy="7" r="3" fill="currentColor" />
                    <rect x="4" y="13" width="12" height="4" rx="2" fill="currentColor" />
                </svg>
                <span>Students</span>
            </a>
            <a href="{% url 'management_courses' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-primary-50 hover:text-primary-700"
               up-target="main" up-transition="cross-fade">
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-primary-600"
                     fill="currentColor" viewBox="0 0 20 20">
                    <rect x="4" y="4" width="12" height="12" rx="2" fill="currentColor" />
                    <rect x="7" y="7" width="6" height="2" rx="1" fill="#fff" />
                    <rect x="7" y="11" width="6" height="2" rx="1" fill="#fff" />
                </svg>
                <span>Courses</span>
            </a>
            <a href="{% url 'management_advising' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-primary-50 hover:text-primary-700"
               up-target="main" up-transition="cross-fade">
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-primary-600"
                     fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 2a8 8 0 100 16 8 8 0 000-16zm1 11H9v-1h2v1zm0-3H9V7h2v3z" fill="currentColor" />
                </svg>
                <span>Advising</span>
            </a>
            <a href="{% url 'management_reports' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-primary-50 hover:text-primary-700"
               up-target="main" up-transition="cross-fade">
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-primary-600"
                     fill="currentColor" viewBox="0 0 20 20">
                    <rect x="4" y="4" width="12" height="12" rx="2" fill="currentColor" />
                    <path d="M8 8h4v4H8z" fill="#fff" />
                </svg>
                <span>Reports</span>
            </a>
        </nav>

        <!-- Footer -->
        <div class="sidebar-footer">
            <div class="sidebar-user">
                <div class="sidebar-avatar">
                    {{ user.get_full_name|default:user.username|slice:":1"|upper }}
                </div>
                <div class="sidebar-user-info">
                    <p>{{ user.get_full_name|default:user.username }}</p>
                    <span>Administrator</span>
                </div>
                <form method="post" action="{% url 'logout' %}" class="ml-auto" up-target="_top">
                    {% csrf_token %}
                    <button type="submit" class="bg-transparent border-0 p-0 text-gray-400 hover:text-red-500 transition-colors duration-200" title="Logout">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                    </button>
                </form>
            </div>
        </div>
    </div>


from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
import json


class Department(models.Model):
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField(blank=True)
    
    def __str__(self):
        return f"{self.code} - {self.name}"


class Course(models.Model):
    DIFFICULTY_CHOICES = [
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
    ]
    
    name = models.CharField(max_length=200)
    code = models.CharField(max_length=20, unique=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE)
    credits = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(6)])
    description = models.TextField()
    prerequisites = models.ManyToManyField('self', blank=True, symmetrical=False)
    difficulty = models.CharField(max_length=20, choices=DIFFICULTY_CHOICES, default='intermediate')
    topics = models.JSONField(default=list, help_text="List of course topics/keywords for content-based matching")
    
    def __str__(self):
        return f"{self.code} - {self.name}"


class StudentProfile(models.Model):
    YEAR_CHOICES = [
        ('freshman', 'Freshman'),
        ('sophomore', 'Sophomore'),
        ('junior', 'Junior'),
        ('senior', 'Senior'),
        ('graduate', 'Graduate'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    student_id = models.CharField(max_length=20, unique=True)
    year = models.CharField(max_length=20, choices=YEAR_CHOICES)
    major = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    interests = models.JSONField(default=list, help_text="List of academic interests/topics")
    career_goals = models.TextField(blank=True)
    preferred_difficulty = models.CharField(max_length=20, choices=Course.DIFFICULTY_CHOICES, default='intermediate')
    date_of_birth = models.DateField(null=True, blank=True)
    phone_number = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    expected_graduation_year = models.IntegerField(null=True, blank=True, validators=[MinValueValidator(2020), MaxValueValidator(2040)])
    
    def __str__(self):
        return f"{self.user.get_full_name()} ({self.student_id})"
    
    @property
    def gpa(self):
        records = self.academicrecord_set.exclude(grade__isnull=True)
        if not records:
            return 0.0
        
        grade_points = {'A': 4.0, 'A-': 3.7, 'B+': 3.3, 'B': 3.0, 'B-': 2.7, 
                       'C+': 2.3, 'C': 2.0, 'C-': 1.7, 'D+': 1.3, 'D': 1.0, 'F': 0.0}
        
        total_points = 0
        total_credits = 0
        for record in records:
            if record.grade in grade_points:
                points = grade_points[record.grade] * record.course.credits
                total_points += points
                total_credits += record.course.credits
        
        return round(total_points / total_credits, 2) if total_credits > 0 else 0.0
    
    @property
    def total_credits(self):
        return sum(record.course.credits for record in self.academicrecord_set.filter(grade__isnull=False))


class AcademicRecord(models.Model):
    GRADE_CHOICES = [
        ('A', 'A'), ('A-', 'A-'), ('B+', 'B+'), ('B', 'B'), ('B-', 'B-'),
        ('C+', 'C+'), ('C', 'C'), ('C-', 'C-'), ('D+', 'D+'), ('D', 'D'), ('F', 'F'),
    ]
    
    SEMESTER_CHOICES = [
        ('fall', 'Fall'),
        ('spring', 'Spring'),
        ('summer', 'Summer'),
    ]
    
    student = models.ForeignKey(StudentProfile, on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    semester = models.CharField(max_length=20, choices=SEMESTER_CHOICES)
    year = models.IntegerField()
    grade = models.CharField(max_length=2, choices=GRADE_CHOICES, null=True, blank=True)
    date_enrolled = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['student', 'course']
    
    def __str__(self):
        return f"{self.student.user.get_full_name()} - {self.course.code} ({self.grade or 'In Progress'})"


class Recommendation(models.Model):
    RECOMMENDATION_TYPES = [
        ('classification', 'Classification-based'),
        ('knowledge', 'Knowledge-based'),
        ('content', 'Content-based'),
        ('hybrid', 'Hybrid'),
    ]
    
    student = models.ForeignKey(StudentProfile, on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    confidence_score = models.FloatField(validators=[MinValueValidator(0.0), MaxValueValidator(1.0)])
    recommendation_type = models.CharField(max_length=20, choices=RECOMMENDATION_TYPES)
    reasoning = models.TextField(help_text="Explanation for why this course was recommended")
    created_at = models.DateTimeField(auto_now_add=True)
    is_dismissed = models.BooleanField(default=False)
    
    class Meta:
        unique_together = ['student', 'course']
        ordering = ['-confidence_score', '-created_at']
    
    def __str__(self):
        return f"{self.student.user.get_full_name()} -> {self.course.code} ({self.confidence_score:.2f})"


class AdvisingSession(models.Model):
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    student = models.ForeignKey(StudentProfile, on_delete=models.CASCADE)
    advisor = models.ForeignKey(User, on_delete=models.CASCADE, related_name='advising_sessions')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, null=True, blank=True)
    date = models.DateTimeField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')
    notes = models.TextField(blank=True)
    
    def __str__(self):
        return f"{self.student.user.get_full_name()} with {self.advisor.get_full_name()} - {self.date.strftime('%Y-%m-%d')}"

{% extends 'student/base.html' %}

{% block title %}Course Recommendations - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">

    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Course Recommendations</h1>
        <p class="mt-2 text-gray-600">Personalized course suggestions based on your academic history, interests, and career goals.</p>
    </div>

    <!-- Recommendations Summary -->
    {% if recommendations %}
    <div class="bg-white rounded-lg shadow mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Your Personalized Recommendations</h2>
            <p class="text-sm text-gray-600 mt-1">{{ recommendations|length }} course{{ recommendations|length|pluralize }} recommended based on your profile</p>
        </div>

        <div class="p-6">
            <!-- High Confidence Recommendations -->
            {% if high_confidence_recommendations %}
            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                        High Match
                    </span>
                    Highly Recommended Courses
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for rec in high_confidence_recommendations %}
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div class="flex items-start justify-between mb-3">
                            <h4 class="text-lg font-semibold text-gray-900">{{ rec.course.code }}</h4>
                            <span class="text-sm font-medium text-green-600">{{ rec.confidence_score|floatformat:0 }}% match</span>
                        </div>
                        <h5 class="text-md font-medium text-gray-800 mb-2">{{ rec.course.title }}</h5>
                        <p class="text-sm text-gray-600 mb-3">{{ rec.course.description|truncatewords:20 }}</p>

                        <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                            <span>{{ rec.course.credits }} credits</span>
                            <span>{{ rec.course.department.code }}</span>
                        </div>

                        {% if rec.reasoning %}
                        <div class="bg-white bg-opacity-50 rounded p-2 mb-3">
                            <p class="text-xs text-gray-700"><strong>Why recommended:</strong> {{ rec.reasoning|truncatewords:15 }}</p>
                        </div>
                        {% endif %}

                        <button hx-post="{% url 'bookmark_course' rec.course.id %}"
                                hx-target="this"
                                hx-swap="outerHTML"
                                class="w-full bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-4 rounded transition-colors">
                            Bookmark Course
                        </button>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Medium Confidence Recommendations -->
            {% if medium_confidence_recommendations %}
            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mr-2">
                        Good Match
                    </span>
                    Good Course Options
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for rec in medium_confidence_recommendations %}
                    <div class="bg-gradient-to-br from-yellow-50 to-amber-50 border border-yellow-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div class="flex items-start justify-between mb-3">
                            <h4 class="text-lg font-semibold text-gray-900">{{ rec.course.code }}</h4>
                            <span class="text-sm font-medium text-yellow-600">{{ rec.confidence_score|floatformat:0 }}% match</span>
                        </div>
                        <h5 class="text-md font-medium text-gray-800 mb-2">{{ rec.course.title }}</h5>
                        <p class="text-sm text-gray-600 mb-3">{{ rec.course.description|truncatewords:20 }}</p>

                        <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                            <span>{{ rec.course.credits }} credits</span>
                            <span>{{ rec.course.department.code }}</span>
                        </div>

                        {% if rec.reasoning %}
                        <div class="bg-white bg-opacity-50 rounded p-2 mb-3">
                            <p class="text-xs text-gray-700"><strong>Why recommended:</strong> {{ rec.reasoning|truncatewords:15 }}</p>
                        </div>
                        {% endif %}

                        <button hx-post="{% url 'bookmark_course' rec.course.id %}"
                                hx-target="this"
                                hx-swap="outerHTML"
                                class="w-full bg-yellow-600 hover:bg-yellow-700 text-white text-sm font-medium py-2 px-4 rounded transition-colors">
                            Bookmark Course
                        </button>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Low Confidence Recommendations -->
            {% if low_confidence_recommendations %}
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                        Consider
                    </span>
                    Other Options to Consider
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for rec in low_confidence_recommendations %}
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div class="flex items-start justify-between mb-3">
                            <h4 class="text-lg font-semibold text-gray-900">{{ rec.course.code }}</h4>
                            <span class="text-sm font-medium text-blue-600">{{ rec.confidence_score|floatformat:0 }}% match</span>
                        </div>
                        <h5 class="text-md font-medium text-gray-800 mb-2">{{ rec.course.title }}</h5>
                        <p class="text-sm text-gray-600 mb-3">{{ rec.course.description|truncatewords:20 }}</p>

                        <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                            <span>{{ rec.course.credits }} credits</span>
                            <span>{{ rec.course.department.code }}</span>
                        </div>

                        {% if rec.reasoning %}
                        <div class="bg-white bg-opacity-50 rounded p-2 mb-3">
                            <p class="text-xs text-gray-700"><strong>Why recommended:</strong> {{ rec.reasoning|truncatewords:15 }}</p>
                        </div>
                        {% endif %}

                        <button hx-post="{% url 'bookmark_course' rec.course.id %}"
                                hx-target="this"
                                hx-swap="outerHTML"
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded transition-colors">
                            Bookmark Course
                        </button>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% else %}
    <!-- No Recommendations -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No recommendations available</h3>
            <p class="mt-1 text-sm text-gray-500">Complete your profile to get personalized course recommendations.</p>
            <div class="mt-6 flex justify-center space-x-3">
                <a href="{% url 'academic_records' %}"
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                    Add Academic Records
                </a>
                <a href="{% url 'student_interests' %}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Add Interests
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% extends 'base.html' %}

{% block title %}Manage Students - {{ block.super }}{% endblock %}



{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div x-data="{ isModalOpen: false }" class="bg-white p-6 rounded-lg shadow-md">
    
    <!-- Header and Add Student Button -->
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-2xl font-semibold text-gray-800">Student Management</h2>
        <button @click="isModalOpen = true" class="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition-transform transform hover:scale-105">
            Add Student
        </button>
    </div>

    <!-- Modal -->
    <div x-show="isModalOpen"
         x-cloak
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60"
         @keydown.escape.window="isModalOpen = false">
        
        <div @click.away="isModalOpen = false" class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
            <div class="flex justify-between items-center mb-4 border-b pb-3">
                <h3 class="text-xl font-semibold text-gray-800">Add New Student</h3>
                <button @click="isModalOpen = false" class="text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
            </div>
            <div class="mt-4">
                <p class="text-gray-600 mb-6">This is where the form to add a new student would go. For now, it's a demonstration of an Alpine.js modal.</p>
            </div>
            <div class="flex justify-end space-x-4 mt-6 pt-4 border-t">
                <button @click="isModalOpen = false" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300">Cancel</button>
                <button class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700">Save Student</button>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="my-6">
        <input type="text" placeholder="Search students by name, ID, or major..." 
               class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
               hx-post="/api/search-students/"
               hx-trigger="keyup changed delay:500ms"
               hx-target="#student-list"
               hx-indicator=".htmx-indicator">
    </div>

    <!-- Student List -->
    <div id="student-list" hx-get="/api/student-list/" hx-trigger="load">
        <div class="htmx-indicator">Loading student list...</div>
    </div>
</div>
{% endblock %}
